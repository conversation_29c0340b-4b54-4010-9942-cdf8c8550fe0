{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.15", "@tailwindcss/postcss": "^4.1.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.518.0", "next": "^15.3.4", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-config-next": "^15.3.4", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "^5.8.3"}}