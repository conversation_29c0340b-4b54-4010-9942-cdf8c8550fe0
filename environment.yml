name: optimed
channels:
  - conda-forge
dependencies:
  # ── core runtime ───────────────────────────────
  - python=3.12
  - pip
  # ── web / async  ───────────────────────────────
  - fastapi
  - uvicorn
  # ── database / vector store ────────────────────
  - psycopg
  - pgvector
  - asyncpg
  # ── type-safe models ───────────────────────────
  - pydantic
  # ── leave anything not on Conda to pip ────────
  - pip:
      - langgraph         # agent framework
      - anthropic         # Claude client
      - "httpx[http2]>=0.27"
      - "fhir.resources>=7.4"
      - "sentence-transformers>=4.1.0"
      - pgvector