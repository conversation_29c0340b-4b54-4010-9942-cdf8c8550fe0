import asyncio
import uuid
from optimed.core.domain import User, UserRole, UserStatus
from optimed.adapters.user_repository.pg_user_repository import PGUserRepository
import os
from dotenv import load_dotenv

async def init_sample_users():
    """Create sample users for testing."""
    load_dotenv()
    
    user_dsn = os.getenv("PGVECTOR_DSN")
    user_repo = PGUserRepository(dsn=user_dsn)
    
    sample_users = [
        User(
            user_id=str(uuid.uuid4()),
            username="dr.smith",
            email="<EMAIL>",
            first_name="<PERSON>",
            last_name="<PERSON>",
            role=UserRole.DOCTOR,
            status=UserStatus.ACTIVE,
            department="Emergency",
            license_number="MD123456"
        ),
        User(
            user_id=str(uuid.uuid4()),
            username="nurse.johnson",
            email="<EMAIL>",
            first_name="<PERSON>",
            last_name="<PERSON>",
            role=UserRole.NURSE,
            status=UserStatus.ACTIVE,
            department="ICU"
        ),
        User(
            user_id=str(uuid.uuid4()),
            username="admin",
            email="<EMAIL>",
            first_name="Admin",
            last_name="User",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            department="IT"
        )
    ]
    
    for user in sample_users:
        try:
            await user_repo.create_user(user)
            print(f"Created user: {user.username} ({user.role.value})")
        except Exception as e:
            print(f"Error creating user {user.username}: {e}")
    
    await user_repo.close()

if __name__ == "__main__":
    asyncio.run(init_sample_users())