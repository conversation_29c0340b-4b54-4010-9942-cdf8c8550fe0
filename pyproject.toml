[build-system]
requires = ["setuptools>=68", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name            = "optimed"
version         = "0.0.0"
description     = "Skeleton package for OptiMed prototype"
authors         = [{ name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" }]
readme          = "README.md"
requires-python = ">=3.12"
dependencies    = []

[tool.setuptools]
package-dir = {"" = "src"} 

[tool.setuptools.packages.find]
where = ["src"]

[tool.mypy]
strict = false
ignore_missing_imports = true
