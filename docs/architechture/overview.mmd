flowchart TD
    %% ── Front-end ───────────────────────────────
    subgraph Browser["Doctor / Nurse workstation"]
        NJS["Next.js UI<br/>• KPI&nbsp;Dashboard<br/>• Chat&nbsp;pane<br/>• SMART-on-FHIR"]
    end

    %% ── Python services ─────────────────────────
    subgraph Backend["Python services (Docker / k8s)"]
        GW["api_gateway<br/>(FastAPI)"] -->|REST / WS| ORCH["LangGraph<br/>Orchestrator"]
        subgraph Adapters
            FHIR["FHIR<br/>Adapter"]
            VEC["pgvector<br/>Adapter"]
            CLAUDE["Claude LLM<br/>Adapter"]
        end
        ORCH --> FHIR
        ORCH --> VEC
        ORCH --> CLAUDE
    end

    %% ── Data plane ──────────────────────────────
    subgraph Data["Stateful components"]
        PG["Postgres<br/>+ pgvector"]
        TS["TimescaleDB"]
        BUS["NATS / RabbitMQ"]
    end
    FHIR <-->|FHIR REST| EHR["Hospital EHR<br/>(Cambio \\| Cerner \\| Epic)"]
    CLAUDE -->|HTTPS| CLAUDEAPI["Anthropic Claude<br/>(EU region)"]

    ORCH -. traces .-> OTEL["OpenTelemetry<br/>Collector"]
    BUS <-->|Lab / Bed events| EHR
    MET["Metrics Engine"] -- writes --> TS
    BUS --> MET

    %% ── Observability & CI/CD ───────────────────
    subgraph Ops["Ops & Visibility"]
        PROM[Prometheus] --> GRA[Grafana]
        GHA["GitHub Actions<br/>CI"]
        ARGO["ArgoCD / Helm"]
    end
    OTEL --> PROM
    GHA -->|build·push| Backend
    ARGO -->|deploy| Backend

    %% ── Browser live KPI websocket ──────────────
    NJS -- OAuth/JWT --> GW
    GW -. fetch KPI .-> TS
    NJS -- WebSocket KPI --> GW
